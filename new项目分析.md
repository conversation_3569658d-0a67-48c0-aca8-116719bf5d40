# SGS-SCM 项目分析要求

## 项目基本信息
- 项目路径：sgs-service/sgs-scm
- 项目描述：SGS管理客户供应链关系的系统
- 文档基础路径：@doc1

## 分析步骤

### Step1: 项目整体架构文档
1. 目标：创建完整的项目大纲文档
2. 输出文件：@README.md
3. 文档维度要求：
   - 项目概述：系统的整体目标、核心功能和业务价值
   - 系统架构：总体架构、技术架构、服务拆分
   - 项目结构：目录结构、核心模块及其职责
   - 业务视角：主要业务流程描述、业务规则
   - 技术视角：设计模式、技术特性、开发规范
   - 数据视角：核心数据模型、实体关系
   - 部署视角：部署架构、环境要求、软件依赖
   - 接口能力：系统提供的核心服务接口概览，**必须使用表格形式**展示，包含接口名称、主要功能描述和详细文档链接
4. 要求：
   - 每个维度需要详细说明并使用流程图、架构图等可视化方式展示
   - 在@doc1目录下创建独立的说明文件，如技术架构说明.md、数据模型说明.md等
   - 与README.md建立引用关系，保持文档间的一致性
   - 接口能力概览必须使用表格形式呈现，而非简单列表，确保与详细API文档格式一致性

### Step2: 业务功能说明
1. 目标：补充系统核心业务能力说明
2. 输出文件：@业务功能说明.md
3. 核心能力范围：
   - 添加客户关系：创建流程、验证规则、审批机制
   - 停用启用关系：状态转换规则、验证条件、权限控制
   - 审批关系申请：审批流程、角色职责、状态变更
4. 要求：
   - 详细说明每个业务能力的业务场景、流程步骤、关键规则
   - 使用流程图和时序图展示业务流程
   - 明确业务主体和角色职责
   - 描述业务规则和约束条件
   - 与README.md建立引用关系

### Step3: 接口能力文档
1. 目标：为每个服务类创建能力说明文档
2. 分析范围：sgs-service/sgs-scm/src/main/java/com/sgs/scm/service/*FacadeServiceImpl.java
3. 文档位置：@doc1/api/*.md
4. 文档命名规则：接口名称.md (例如：ISCMRelationshipFacadeService.md)
5. 文档结构要求：
   - 能力大纲：表格形式展示所有方法及其功能描述
   - 每个能力的详细说明，包含：
     - 逻辑视图：使用mermaid绘制的时序图或流程图
     - 功能说明：简明扼要的功能描述
     - 业务规则：遵循的核心业务规则
     - 前置条件：调用接口前必须满足的条件
     - 后置条件：接口调用成功后的系统状态
     - 详细描述：包含接口定义、输入参数、输出参数等
6. 特殊要求：
   - 参考模板：sgs-service/sgs-scm/doc/FacadeDemo.md
   - 识别@ApiOperation注解作为接口能力说明
   - 识别@ApiModel注解作为业务实体对象
   - 提供数据库对象、DTO实体的ER关系图
   - 明确接口的输入/输出实体信息及参数说明
   - 构建核心属性影响业务流程表
   - 构建业务规则详细表
   - 展示关系状态更新规则的枚举定义
   - 提供优化的业务流程时序图和数据流图
   - 列举使用的枚举类内容
   - 使用表格形式呈现接口和规则描述
   - 不包含private类型函数
   - **对于基础信息验证规则（如长度判断、类型判断等），应使用专门的实体验证规则文档，在接口文档中通过引用方式处理**

### Step4: 实体验证规则文档
1. 目标：创建专门的文档来描述所有业务实体对象的基础验证规则
2. 输出文件：@doc1/data/实体验证规则.md
3. 文档结构要求：
   - 按实体对象分类列出所有基础验证规则
   - 使用表格形式展示验证规则
   - 表格应包含：字段名称、验证规则、错误码、错误消息
   - 区分不同实体对象的验证规则
4. 表格结构：
   - 字段名称：实体对象的属性名称
   - 验证规则：描述验证内容（如非空、长度范围、格式要求等）
   - 错误码：验证失败时返回的错误码
   - 错误消息：验证失败时的提示信息
5. 特殊要求：
   - 聚焦于基础数据验证，如长度校验、类型校验、非空校验等
   - 不包含业务逻辑规则（业务规则应在接口文档中描述）
   - 实体对象应与API文档中使用的输入输出对象保持一致
   - 提供访问链接，便于API文档引用
6. 示例内容：
   ```markdown
   ## CompanyInfoDTO 验证规则
   
   | 字段名称 | 验证规则 | 错误码 | 错误消息 |
   |---------|----------|--------|----------|
   | taxNo | 非空，长度[10,50] | PARAMS_BODY_ERROR_CODE | 税务编号不能为空/税务编号长度必须在10-50之间 |
   | bossNo | 长度≤50 | PARAMS_INVALID_ERROR_CODE | 负责人编号长度不能超过50 |
   ```

## 文档一致性要求

1. 总体文档与详细文档一致性：
   - README.md中的接口能力概览须与详细API文档保持格式一致性，两者均使用表格形式展示
   - README.md中的接口能力概览表格至少包含：接口名称、主要功能、链接三列
   - 详细API文档中的能力大纲表格应当更全面，包含：方法名、功能描述、输入参数、输出参数四列
   - 表格样式应统一，标题行加粗，内容对齐，确保视觉一致性
   - 接口描述语言风格应保持一致，避免风格不一致

2. 示例格式（README.md接口能力表格）：
   ```
   | 接口名称 | 主要功能 | 详细文档 |
   |---------|----------|----------|
   | ISCMRelationshipFacadeService | 客户关系管理服务，提供关系创建、查询、状态更新等功能 | [详细文档](api/ISCMRelationshipFacadeService.md) |
   | ICompanyFacadeService | 公司信息管理服务，提供公司信息添加、查询等功能 | [详细文档](api/ICompanyFacadeService.md) |
   | ISCMApprovalFacadeService | 审批流程管理服务，提供审批规则管理、流程处理等功能 | [详细文档](api/ISCMApprovalFacadeService.md) |
   ```

## 业务规则与基础验证规则区分

1. 业务规则与基础验证规则区分原则：
   - 业务规则：关注业务逻辑和业务流程控制，体现领域知识和业务约束，应在接口文档中详细描述
   - 基础验证规则：关注数据格式、长度、类型等基础验证，属于技术实现细节，应在实体验证规则文档中描述
   
2. 接口文档中业务规则的处理：
   - 接口文档中的业务规则部分应聚焦于真正的业务逻辑规则
   - 对于涉及的实体对象基础验证，使用引用方式链接到实体验证规则文档
   - 前置条件中可以引用实体验证规则文档的相关部分
   
3. 引用方式示例：
   ```markdown
   #### 2.1.4 前置条件
   
   - [ApplyRelationshipCmd参数验证规则](../data/实体验证规则.md#applyrelationshipcmd-验证规则)
   - 其他业务前置条件...
   ```

## 核心属性影响业务流程表要求

1. 目标：清晰展示关键属性如何影响业务处理逻辑
2. 表格结构：
   - 属性名：影响业务流程的核心属性名称
   - 来源：属性数据的来源（外部请求、系统计算、配置等）
   - 影响描述：属性对业务流程的影响说明
   - 分支条件：具体的代码条件表达式
   - 异常码：如果触发异常，对应的异常码
   - 异常信息：对应的异常消息内容
   - 日志级别：记录的日志级别（ERROR、WARN、INFO等）
3. 示例内容：
   ```
   | 属性名 | 来源 | 影响描述 | 分支条件 | 异常码 | 异常信息 | 日志级别 |
   |--------|------|----------|----------|--------|----------|----------|
   | parentCompanyId | 外部请求 | 父公司ID为空时抛出异常 | parentCompanyId == null | PARAMS_BODY_ERROR_CODE | 父公司ID不能为空 | ERROR |
   ```

## 业务规则详细表要求

1. 目标：聚焦真正的业务逻辑规则，而非基础数据验证
2. 表格结构：
   - 规则名称：业务规则的名称，使用"XX规则"的命名方式
   - 数据来源：规则判断所需数据的来源（数据库查询、领域模型、缓存等）
   - 验证规则来源：规则定义的来源（领域策略、业务配置、状态机定义等）
   - 规则描述：规则的详细描述
   - 违规后果：规则违反后的处理（拒绝操作、降级处理、触发通知等）
   - 异常码：如果触发异常，对应的异常码
   - 异常信息：对应的异常消息内容
   - 日志级别：记录的日志级别（ERROR、WARN、INFO等）
3. 规则分类指南：
   - 业务规则应关注业务流程和领域逻辑
   - 不包含基础数据验证（如长度校验、类型校验、非空校验等）
   - 应体现领域驱动设计思想
4. 示例内容：
   ```
   | 规则名称 | 数据来源 | 验证规则来源 | 规则描述 | 违规后果 | 异常码 | 异常信息 | 日志级别 |
   |----------|----------|--------------|----------|----------|--------|----------|----------|
   | 关系唯一性规则 | 数据库查询 | 领域策略 | 同一客户不能与同一供应商建立相同类型的关系 | 拒绝创建 | REPEAT_ERROR_CODE | 关系已经存在 | ERROR |
   ```

## 文档格式要求

1. 流程图要求：
   - 使用mermaid语法绘制流程图、时序图和ER图
   - 美观清晰，避免过度复杂
   - 明确标记接口能力和处理流程
   - 标记业务实体对象影响点
   - 区分不同主体（如客户端、服务、数据库等）
   - 展示关键决策点和分支逻辑

2. 表格要求：
   - 使用markdown表格语法
   - 表头清晰，内容对齐
   - 必要时使用合并单元格提高可读性
   - 对长文本内容进行适当换行处理
   - 保持一致的格式和风格

3. 代码示例要求：
   - 使用```java 和 ``` 包裹代码块
   - 提供清晰的方法签名和参数说明
   - 包含必要的注释说明
   - 遵循Java代码规范

4. 文档组织要求：
   - 使用层级标题组织内容
   - 保持章节编号的一致性
   - 相关内容应放在一起
   - 使用引用链接关联相关文档
   - 必要时使用引用注释说明特殊情况

5. 多媒体内容：
   - 流程图应使用mermaid语法
   - 复杂表格可使用HTML表格语法
   - 必要时可插入外部图片（提供图片URL）

## 质量检查清单

在提交文档前，请检查以下项目：

1. 内容完整性：
   - 所有要求的章节都已包含
   - 没有遗漏关键信息
   - 没有TO-DO或未完成的部分

2. 一致性：
   - 术语使用一致
   - 格式风格一致
   - 与其他文档的引用关系正确

3. 准确性：
   - 业务流程描述准确
   - 技术实现描述准确
   - 接口参数描述准确

4. 可读性：
   - 语言表达清晰
   - 结构组织合理
   - 图表易于理解

5. 实用性：
   - 文档对开发人员有帮助
   - 文档能解答常见问题
   - 文档便于维护和更新 