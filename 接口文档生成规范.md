# 接口文档生成规范与提示词

## 概述
本文档提供了一套标准化的接口文档生成规范，基于实际项目优化经验，确保生成高质量、准确性和可操作性的技术文档。

---

## 🎯 文档生成标准提示词

### 基础要求提示词

```
作为资深Java架构师，请为 [接口/服务名称] 生成详细的技术文档。

核心要求：
1. 所有描述必须基于实际代码实现，确保100%准确性
2. 消除冗余和重复内容，整合分散的业务规则
3. 提供可操作的技术细节和具体实现说明
4. 使用标准化的文档结构和格式
5. 重点突出关键业务逻辑和核心算法

请严格按照以下文档结构生成：
```

### 标准文档结构模板

#### 1. 文档头部信息
```markdown
# [服务名称]实现说明

## 文档说明
本文档描述了[服务类名]的主要业务能力，[简要业务描述]。重点说明了[关键方法名]等关键业务逻辑，并整合了分散的验证规则和业务处理规则。

## 业务场景
- [主要业务场景1]
- [主要业务场景2]
- [差异化处理场景]

## 涉及业务实体
- [实体1]（[实体说明]）
- [实体2]（[实体说明]）
```

#### 2. 业务实体关系图
```markdown
## 业务实体关系图
```mermaid
erDiagram
    实体A ||--o{ 实体B : 关系描述
    实体B ||--|| 实体C : 关系描述
```
```

#### 3. 核心能力接口列表
```markdown
## 核心能力接口列表

### 主要处理接口
| 接口名称 | 接口描述 | 影响业务实体 | 入参对象 | 出参对象 | 详细说明 |
|---------|----------|--------------|----------|----------|----------|
| [方法名] | [方法描述] | [影响实体] | [入参类型] | [出参类型] | [详细说明链接] |
```

#### 4. 业务实体对象详细说明
```markdown
## 业务实体对象说明

### [实体名称]
| 字段名 | 类型 | 说明 | 关联实体 |
|--------|------|------|----------|
| [字段1] | [类型] | [说明] | [关联实体] |
```

#### 5. 核心业务流程
```markdown
## 核心业务能力详细说明

### [主要方法名]

#### 状态流转
```mermaid
stateDiagram-v2
    [*] --> 状态1: 触发条件
    状态1 --> 状态2: 转换条件
```

#### 处理流程详细图
```mermaid
flowchart TD
    Start([开始]) --> A[处理步骤1]
    A --> B{判断条件}
    B -->|成功| C[处理步骤2]
    B -->|失败| E[错误处理]
```
```

#### 6. 关键业务逻辑详解
```markdown
## 关键业务逻辑详解

### [关键方法名]算法详解

**方法签名和参数说明**：
```java
[完整方法签名]
```

**算法策略优先级**：
| 优先级 | 策略名称 | 触发条件 | 匹配逻辑 | 业务场景 |
|--------|----------|----------|----------|----------|

**失败处理机制**：
[详细描述失败时的处理逻辑]
```

#### 7. 统一规则矩阵
```markdown
## 统一验证规则矩阵

### [阶段名称]验证规则
| 验证项 | 验证条件 | 错误信息 | 验证逻辑代码实现 | 失败影响 |
|--------|----------|----------|----------------|----------|

## 统一业务处理规则详解

### [阶段名称]业务规则
| 处理项 | 处理逻辑 | 调用接口/方法 | 成功标准 | 失败处理 |
|--------|----------|------------|----------|----------|
```

#### 8. 差异化处理规则
```markdown
## [维度名称]差异化处理规则

### 综合处理规则矩阵
| [维度1] | [维度2] | [关键标识] | [处理阶段1] | [处理阶段2] | [主要应用场景] |
|---------|---------|------------|-------------|-------------|-------------|
```

#### 9. 技术要素
```markdown
## 技术要素和监控指南

### 核心枚举定义
| 枚举类型 | 关键值 | 说明 | 业务影响 |
|---------|--------|------|----------|

### 关键错误类型和处理策略
| 错误级别 | 典型错误信息 | 处理策略 | 业务影响 |
|---------|-------------|----------|----------|

### 性能监控指标
| 监控维度 | 关键指标 | 正常范围 | 告警阈值 | 处理建议 |
|---------|----------|----------|----------|----------|
```

#### 10. 问题排查指南
```markdown
## 问题排查指南

### 问题分类和快速诊断
| 问题类型 | 典型现象 | 快速排查点 | 常见解决方案 |
|---------|----------|------------|-------------|

### 关键日志位置和分析方法
| 日志类型 | 关键字段 | 排查重点 | 分析建议 |
|---------|----------|----------|----------|
```

#### 11. 版本变更记录
```markdown
## 版本变更记录

| 版本号 | 变更日期 | 变更内容 | 影响范围 | 变更人 |
|--------|----------|----------|----------|--------|
| x.x.x | YYYY-MM-DD | [变更说明] | [影响范围] | [变更人] |
```

---

## 📋 文档质量检查清单

### 准确性检查
- [ ] **代码一致性**：所有描述是否与实际代码实现完全一致
- [ ] **枚举值准确**：所有枚举值、常量值是否准确无误
- [ ] **方法调用链**：方法调用关系和参数传递是否正确
- [ ] **业务逻辑**：复杂业务逻辑的算法描述是否准确

### 完整性检查
- [ ] **核心方法覆盖**：关键业务方法是否都有详细说明
- [ ] **异常处理**：异常情况和错误处理是否完整描述
- [ ] **配置依赖**：外部配置和依赖关系是否说明清楚
- [ ] **数据流转**：数据在各个阶段的流转是否清晰

### 可读性检查
- [ ] **结构清晰**：章节组织是否逻辑清晰
- [ ] **表格规范**：表格信息是否完整、格式统一
- [ ] **图表准确**：流程图和关系图是否准确反映实际逻辑
- [ ] **术语一致**：技术术语使用是否前后一致

### 实用性检查
- [ ] **操作指导**：是否提供具体的操作和配置指导
- [ ] **问题排查**：是否提供实用的问题排查方法
- [ ] **监控建议**：是否提供有效的监控和告警建议
- [ ] **维护指南**：是否便于后续维护和扩展

---

## 🔧 实施指导

### 代码分析步骤

#### 1. 静态代码分析
```
1. 识别主要业务类和核心方法
2. 分析类之间的依赖关系和调用链
3. 提取关键枚举、常量和配置
4. 识别异常处理机制和错误码
```

#### 2. 业务逻辑梳理
```
1. 梳理主要业务流程和状态流转
2. 分析复杂算法和核心业务规则
3. 识别差异化处理逻辑和分支条件
4. 整理验证规则和业务约束
```

#### 3. 接口关系分析
```
1. 分析入参出参的数据结构
2. 梳理与外部系统的接口调用
3. 识别数据库操作和事务处理
4. 分析缓存使用和性能优化点
```

### 文档编写技巧

#### 1. 表格设计原则
- **信息完整**：每个表格包含完整的相关信息
- **逻辑清晰**：列的设计要符合逻辑关系
- **便于查找**：重要信息放在显眼位置
- **格式统一**：相同类型的表格使用相同格式

#### 2. 流程图绘制要求
- **准确反映代码逻辑**：每个判断节点都要有对应的代码实现
- **完整覆盖分支**：包含所有可能的执行路径
- **清晰标注条件**：判断条件要具体明确
- **错误处理路径**：包含异常处理的流程

#### 3. 代码示例规范
- **关键代码段**：展示核心业务逻辑的代码片段
- **参数说明**：详细说明方法参数的含义和约束
- **返回值描述**：说明返回值的结构和可能的值
- **异常情况**：列出可能抛出的异常和处理方式

---

## 📈 质量改进建议

### 持续优化策略
1. **定期代码同步**：确保文档与代码实现保持同步
2. **用户反馈收集**：收集使用者的反馈和改进建议
3. **版本管理**：建立完善的文档版本管理机制
4. **知识沉淀**：将问题排查经验及时补充到文档中

### 团队协作规范
1. **责任分工**：明确文档维护的责任人
2. **评审机制**：建立文档评审和质量检查机制
3. **培训指导**：对团队成员进行文档编写培训
4. **工具统一**：使用统一的文档编写和管理工具

---

## 🎨 高级技巧

### 复杂业务逻辑描述技巧
1. **分层描述**：从概述到细节，层层递进
2. **场景举例**：用具体业务场景说明抽象逻辑
3. **对比分析**：通过对比不同情况加深理解
4. **可视化展示**：用图表和流程图增强可读性

### 技术细节展现方法
1. **参数映射表**：清晰展示参数转换和映射关系
2. **状态机图**：用于描述复杂的状态变化
3. **时序图**：展示系统间的交互时序
4. **架构图**：说明系统的整体架构设计

### 维护性增强措施
1. **模块化组织**：将文档按功能模块组织
2. **交叉引用**：建立章节间的交叉引用
3. **索引目录**：提供详细的索引和目录
4. **搜索优化**：使用关键词标签便于搜索

---

## 📝 模板使用说明

### 快速开始
1. 复制本规范中的"标准文档结构模板"
2. 根据实际业务需求调整章节内容
3. 按照"代码分析步骤"进行代码调研
4. 使用"文档质量检查清单"进行自查

### 自定义扩展
- 根据业务特点增加专门的章节
- 调整表格结构以适应具体信息
- 增加业务相关的图表类型
- 补充行业特定的术语解释

### 持续改进
- 定期根据使用经验更新规范
- 收集团队反馈优化模板结构
- 建立最佳实践案例库
- 形成团队内部的文档标准

---

*规范版本：v1.0*  
*最后更新：2024年12月*  
*维护团队：技术文档规范化小组* 