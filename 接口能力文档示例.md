# 接口能力文档示例

本文档展示如何使用通用项目分析规范模板中新增的接口能力文档结构。

## 核心能力接口列表

### 主要处理接口
| 接口名称 | 接口描述 | 影响业务实体 | 入参对象 | 出参对象 | 详细说明 |
|---------|----------|-------------|----------|----------|----------|
| createSubContract | 创建分包合同 | SubContract, Order | CreateSubContractRequest | SubContractResponse | [详细说明](#createsubcontract-详细说明) |
| updateSubContractStatus | 更新分包状态 | SubContract | UpdateStatusRequest | BaseResponse | [详细说明](#updatesubcontractstatus-详细说明) |
| querySubContractList | 查询分包列表 | SubContract, Order | QuerySubContractRequest | SubContractListResponse | [详细说明](#querysubcontractlist-详细说明) |
| deleteSubContract | 删除分包合同 | SubContract | DeleteSubContractRequest | BaseResponse | [详细说明](#deletesubcontract-详细说明) |

## 业务实体对象说明

### SubContract（分包）
| 字段名 | 类型 | 说明 | 关联实体 |
|--------|------|------|----------|
| id | Long | 主键ID | - |
| no | String | 分包编号 | - |
| orderNo | String | 订单编号 | Order |
| status | Enum | 分包状态 | - |
| productLineType | Enum | 产品线类型 | - |
| contractAmount | BigDecimal | 合同金额 | - |
| startDate | Date | 开始日期 | - |
| endDate | Date | 结束日期 | - |
| createTime | Date | 创建时间 | - |
| updateTime | Date | 更新时间 | - |

### Order（订单）
| 字段名 | 类型 | 说明 | 关联实体 |
|--------|------|------|----------|
| id | Long | 主键ID | - |
| orderNo | String | 订单编号 | - |
| customerName | String | 客户名称 | Customer |
| totalAmount | BigDecimal | 订单总金额 | - |
| status | Enum | 订单状态 | - |
| createTime | Date | 创建时间 | - |
| updateTime | Date | 更新时间 | - |

### Customer（客户）
| 字段名 | 类型 | 说明 | 关联实体 |
|--------|------|------|----------|
| id | Long | 主键ID | - |
| customerCode | String | 客户编码 | - |
| customerName | String | 客户名称 | - |
| contactPhone | String | 联系电话 | - |
| address | String | 客户地址 | - |
| createTime | Date | 创建时间 | - |
| updateTime | Date | 更新时间 | - |

## 业务实体关系图

```mermaid
erDiagram
    Customer ||--o{ Order : "拥有"
    Order ||--o{ SubContract : "包含"
    
    Customer {
        Long id PK
        String customerCode
        String customerName
        String contactPhone
        String address
        Date createTime
        Date updateTime
    }
    
    Order {
        Long id PK
        String orderNo
        String customerName
        BigDecimal totalAmount
        Enum status
        Date createTime
        Date updateTime
    }
    
    SubContract {
        Long id PK
        String no
        String orderNo FK
        Enum status
        Enum productLineType
        BigDecimal contractAmount
        Date startDate
        Date endDate
        Date createTime
        Date updateTime
    }
```

## 验证规则说明

### 业务验证规则
在接口处理过程中需要验证的业务逻辑规则：

| 规则名称 | 数据来源 | 验证规则来源 | 规则描述 | 违规后果 | 异常码 | 异常信息 | 日志级别 |
|----------|----------|--------------|----------|----------|--------|----------|----------|
| 分包唯一性规则 | 数据库查询 | 业务策略 | 同一订单下分包编号不能重复 | 拒绝创建 | SUBCONTRACT_NO_DUPLICATE | 分包编号已存在 | ERROR |
| 订单状态规则 | 数据库查询 | 状态机定义 | 只有有效状态的订单才能创建分包 | 拒绝创建 | ORDER_STATUS_INVALID | 订单状态不允许创建分包 | ERROR |
| 金额范围规则 | 业务配置 | 业务配置 | 分包金额不能超过订单剩余金额 | 拒绝创建 | AMOUNT_EXCEED_LIMIT | 分包金额超出限制 | ERROR |

### 基础验证规则
引用实体验证规则文档中的基础数据验证：
- [SubContract参数验证规则](../data/实体验证规则.md#subcontract-验证规则)
- [Order参数验证规则](../data/实体验证规则.md#order-验证规则)
- [Customer参数验证规则](../data/实体验证规则.md#customer-验证规则)

## createSubContract-详细说明

### 功能说明
创建新的分包合同，建立订单与分包的关联关系。

### 逻辑视图
```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Service as 分包服务
    participant OrderService as 订单服务
    participant DB as 数据库
    
    Client->>Service: createSubContract(request)
    Service->>Service: 基础参数验证
    Service->>OrderService: 查询订单信息
    OrderService-->>Service: 返回订单详情
    Service->>Service: 业务规则验证
    Service->>DB: 保存分包信息
    DB-->>Service: 返回保存结果
    Service-->>Client: 返回创建结果
```

### 前置条件
- [CreateSubContractRequest参数验证规则](../data/实体验证规则.md#createsubcontractrequest-验证规则)
- 订单必须存在且状态为有效
- 用户具有创建分包的权限

### 后置条件
- 分包记录成功创建
- 订单状态可能发生变更
- 生成分包创建日志

### 核心属性影响业务流程表
| 属性名 | 来源 | 影响描述 | 分支条件 | 异常码 | 异常信息 | 日志级别 |
|--------|------|----------|----------|--------|----------|----------|
| orderNo | 外部请求 | 订单编号为空时抛出异常 | orderNo == null | PARAMS_BODY_ERROR_CODE | 订单编号不能为空 | ERROR |
| contractAmount | 外部请求 | 金额超出限制时拒绝创建 | amount > orderRemainAmount | AMOUNT_EXCEED_LIMIT | 分包金额超出订单剩余金额 | ERROR |
| productLineType | 外部请求 | 产品线类型决定审批流程 | productLineType == HIGH_RISK | - | 高风险产品线需要额外审批 | INFO |

## 使用说明

### 如何应用此示例
1. **复制结构**: 将此示例的结构复制到您的项目文档中
2. **替换内容**: 根据实际项目替换实体名称、字段、接口等
3. **调整关系**: 根据实际业务调整实体间的关系
4. **验证规则**: 确保验证规则与代码逻辑一致

### 关键要点
- **实体关系图**: 清晰展示业务实体间的关联关系
- **接口列表**: 提供快速的接口概览
- **验证规则**: 区分业务规则和基础验证规则
- **代码一致性**: 确保文档与实际代码逻辑保持一致

### 扩展建议
- 可以增加更多的业务实体
- 可以添加枚举值的详细说明
- 可以补充更复杂的业务流程图
- 可以增加异常处理的详细说明
