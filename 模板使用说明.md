# 通用项目分析规范模板使用说明

## 概述
本文档说明如何使用 `通用项目分析规范模板.md` 来为新项目创建标准化的分析文档。

## 模板特点

### 1. 通用性
- 去除了特定项目的个性化内容
- 使用占位符 `{变量名}` 标记需要替换的内容
- 适用于多种类型的软件项目

### 2. 结构化
- 4个标准分析步骤
- 明确的文档输出要求
- 统一的格式规范

### 3. 可扩展性
- 可根据项目特点增删章节
- 支持不同技术栈的适配
- 提供扩展建议

## 使用步骤

### 第一步：复制模板
```bash
cp 通用项目分析规范模板.md 新项目分析要求.md
```

### 第二步：配置项目基本信息
替换以下核心变量：
- `{PROJECT_PATH}`: 实际项目路径
- `{PROJECT_DESCRIPTION}`: 项目功能描述  
- `{DOC_BASE_PATH}`: 文档根目录

### 第三步：定制业务能力
根据项目实际业务，替换：
- `{BUSINESS_CAPABILITY_1/2/3}`: 具体业务能力名称和描述

### 第四步：配置技术路径
- `{SERVICE_CODE_PATH}`: 服务代码实际路径
- `{INTERFACE_NAME_EXAMPLE}`: 接口命名示例
- `{TEMPLATE_PATH}`: 如有参考模板，填入路径

### 第五步：调整示例内容
替换所有示例中的占位符，如：
- `{ENTITY_NAME}`: 实际实体名称
- `{ERROR_CODE}`: 项目错误码规范
- `{SERVICE_NAME_X}`: 实际服务名称

## 变量替换对照表

| 占位符 | 说明 | 示例 |
|--------|------|------|
| `{PROJECT_PATH}` | 项目代码路径 | `services/user-service` |
| `{PROJECT_DESCRIPTION}` | 项目描述 | `用户管理服务系统` |
| `{DOC_BASE_PATH}` | 文档根目录 | `@doc` 或 `docs/` |
| `{BUSINESS_CAPABILITY_1}` | 业务能力1 | `用户注册管理` |
| `{SERVICE_CODE_PATH}` | 服务代码路径 | `src/main/java/com/company/service/*Service.java` |
| `{INTERFACE_NAME_EXAMPLE}` | 接口名称示例 | `IUserService.md` |
| `{ENTITY_NAME}` | 实体名称 | `UserInfo` |
| `{ERROR_CODE}` | 错误码 | `USER_NOT_FOUND` |

## 适配不同项目类型

### Java Spring Boot项目
- 服务代码路径：`src/main/java/com/company/service/*Service.java`
- 注解识别：`@RestController`, `@Service`, `@ApiOperation`
- 实体注解：`@Entity`, `@ApiModel`

### Node.js项目  
- 服务代码路径：`src/services/*.js` 或 `src/controllers/*.js`
- 注解识别：JSDoc注释、Swagger注解
- 实体定义：TypeScript接口、Mongoose Schema

### Python项目
- 服务代码路径：`src/services/*.py` 或 `app/api/*.py`
- 注解识别：FastAPI装饰器、Flask路由
- 实体定义：Pydantic模型、SQLAlchemy模型

### .NET项目
- 服务代码路径：`Services/*Service.cs` 或 `Controllers/*Controller.cs`
- 注解识别：`[ApiController]`, `[HttpGet]`, `[ApiOperation]`
- 实体定义：`[DataContract]`, Entity Framework模型

## 常见定制场景

### 1. 微服务项目
- 增加服务间通信分析
- 添加服务发现和配置管理
- 补充分布式事务处理

### 2. 前端项目
- 调整为组件分析
- 增加状态管理分析
- 添加路由和页面流程

### 3. 数据处理项目
- 重点关注数据流程
- 增加数据质量规则
- 添加性能指标要求

### 4. API网关项目
- 重点分析路由规则
- 增加认证授权机制
- 添加限流和监控

## 质量保证

### 检查清单
- [ ] 所有占位符已替换
- [ ] 业务能力描述准确
- [ ] 技术路径配置正确
- [ ] 示例内容符合项目实际
- [ ] 文档结构完整

### 常见问题
1. **忘记替换占位符**: 使用搜索功能查找所有 `{` 和 `}` 
2. **路径配置错误**: 确认项目实际目录结构
3. **业务描述不准确**: 与业务人员确认核心能力
4. **技术栈不匹配**: 根据实际技术栈调整注解和路径

## 维护建议

### 版本管理
- 为模板建立版本号
- 记录每次修改的原因
- 保持向后兼容性

### 持续改进
- 收集使用反馈
- 总结最佳实践
- 定期更新模板

### 团队协作
- 建立模板使用培训
- 制定文档评审流程
- 分享成功案例

## 支持与反馈

如果在使用过程中遇到问题或有改进建议，请：
1. 检查本使用说明
2. 参考模板中的示例
3. 与团队技术负责人讨论
4. 提出模板改进建议
